syntax = "proto3";

package iAnts;

import "iants.proto";

// SpringBoard 专用消息类型
enum SBMessageType {
  SB_UNKNOWN = 0;           // 第一个值必须是0
  SB_SHOW_ALERT = 1;        // 弹窗
  SB_SHOW_TOAST = 2;        // Toast 
  SB_TOUCH_EVENT = 3;       // 触摸事件
  SB_NOTIFICATION = 4;      // 通知
  SB_FOREGROUND_APP = 5;    // 前台应用
  SB_SCREENSHOT = 6;        // 截图
  SB_LOCK_SCREEN = 7;       // 锁屏
  SB_UNLOCK_SCREEN = 8;     // 解锁
  SB_ADVANCED_ALERT_BOX = 9; // 高级弹窗
  SB_PASTE = 10;            // 粘贴动作
}

// SpringBoard 专用消息体

// 弹窗，无回复
message SB_Alert {
  string title = 1;
  string content = 2;
  int32 dismissTime = 3;
}

// Toast 无回复
message SB_Toast {
  string content = 1;
  int32 type = 2;
  float duration = 3;
  int32 position = 4;
  int32 fontSize = 5;
}

// 触摸事件，无回复
message SB_TouchEvent {
  int32 x = 1;
  int32 y = 2;
  string action = 3;
}

// 通知，无回复
message SB_Notification {
  string title = 1;
  string body = 2;
}

// 前台应用，有回复
message SB_ForegroundApp {
  string request_id = 1;        // 请求ID（请求和响应都需要）
  string bundle_id = 2;         // 响应时填充：应用Bundle ID
  string app_name = 3;          // 响应时填充：应用名称（可选）
  bool success = 4;             // 响应时填充：是否成功获取
  string error_message = 5;     // 响应时填充：错误信息（可选）
}

// 截图，有回复 
// 两种情况：1. 请求截图只保存到本地  2. 请求截图（isBase64==True）并返回图片base64
message SB_Screenshot {
  string request_id = 1;        // 请求ID（请求和响应都需要）
  string path = 2;              // 响应时填充：保存路径
  bool isBase64 = 3;            // 请求时填充：是否返回base64数据
  bool isCompress = 4;          // 请求时填充：是否开启压缩
  string data = 5;              // 响应时填充：base64图片数据（如果请求了base64）
  bool success = 6;             // 响应时填充：是否成功
  string error_message = 7;     // 响应时填充：错误信息（可选）
}

// 锁屏动作，无回复
message SB_LockScreen {
  // 空消息
}

// 解锁动作，无回复
message SB_UnlockScreen {
  // 空消息
}

// 高级弹窗，无回复
message SB_AdvancedAlert {
  string title = 1;
  string content = 2;
  string defaultButtonTitle = 3;
  string alternateButtonTitle = 4;
  string otherButtonTitle = 5;
  repeated string checkBoxTitles = 6;
  repeated string textFieldTitles = 7;
  repeated string textFieldValues = 8;
  repeated string popUpTitles = 9;
  int32 popUpSelection = 10;
  float progressValue = 11;
  string iconURL = 12;
  int32 dismissTime = 13;
}

// 发送粘贴内容，无回复
message SB_Paste {
  string content = 1;
}

// SpringBoard 完整消息
message SBMessage {
  oneof message_type {
    CommonMessageType common_type = 1;
    SBMessageType sb_type = 2;
  }

  oneof body {
    // 公共消息
    Heartbeat heartbeat = 10;
    ErrorMessage error = 11;
    
    // SB 专用消息
    SB_Alert sb_alert = 20;
    SB_Toast sb_toast = 21;
    SB_TouchEvent sb_touch_event = 22;
    SB_ForegroundApp sb_foreground_app = 23;
    SB_Notification sb_notification = 24;
    SB_Screenshot sb_screenshot = 25;
    SB_LockScreen sb_lock_screen = 26;
    SB_UnlockScreen sb_unlock_screen = 27;
    SB_AdvancedAlert sb_advanced_alert = 28;
    SB_Paste sb_paste = 29;
  }
}
