// DO NOT EDIT.
// swift-format-ignore-file
// swiftlint:disable all
//
// Generated by the Swift generator plugin for the protocol buffer compiler.
// Source: iants_sb.proto
//
// For information on using the generated types, please see the documentation:
//   https://github.com/apple/swift-protobuf/

import SwiftProtobuf

// If the compiler emits an error on this type, it is because this file
// was generated by a version of the `protoc` Swift plug-in that is
// incompatible with the version of SwiftProtobuf to which you are linking.
// Please ensure that you are building against the same version of the API
// that was used to generate this file.
fileprivate struct _GeneratedWithProtocGenSwiftVersion: SwiftProtobuf.ProtobufAPIVersionCheck {
  struct _2: SwiftProtobuf.ProtobufAPIVersion_2 {}
  typealias Version = _2
}

/// SpringBoard 专用消息类型
enum IAnts_SBMessageType: SwiftProtobuf.Enum, Swift.CaseIterable {
  typealias RawValue = Int

  /// 第一个值必须是0
  case sbUnknown // = 0

  /// 弹窗
  case sbShowAlert // = 1

  /// Toast 
  case sbShowToast // = 2

  /// 触摸事件
  case sbTouchEvent // = 3

  /// 通知
  case sbNotification // = 4

  /// 前台应用
  case sbForegroundApp // = 5

  /// 截图
  case sbScreenshot // = 6

  /// 锁屏
  case sbLockScreen // = 7

  /// 解锁
  case sbUnlockScreen // = 8

  /// 高级弹窗
  case sbAdvancedAlertBox // = 9

  /// 粘贴动作
  case sbPaste // = 10
  case UNRECOGNIZED(Int)

  init() {
    self = .sbUnknown
  }

  init?(rawValue: Int) {
    switch rawValue {
    case 0: self = .sbUnknown
    case 1: self = .sbShowAlert
    case 2: self = .sbShowToast
    case 3: self = .sbTouchEvent
    case 4: self = .sbNotification
    case 5: self = .sbForegroundApp
    case 6: self = .sbScreenshot
    case 7: self = .sbLockScreen
    case 8: self = .sbUnlockScreen
    case 9: self = .sbAdvancedAlertBox
    case 10: self = .sbPaste
    default: self = .UNRECOGNIZED(rawValue)
    }
  }

  var rawValue: Int {
    switch self {
    case .sbUnknown: return 0
    case .sbShowAlert: return 1
    case .sbShowToast: return 2
    case .sbTouchEvent: return 3
    case .sbNotification: return 4
    case .sbForegroundApp: return 5
    case .sbScreenshot: return 6
    case .sbLockScreen: return 7
    case .sbUnlockScreen: return 8
    case .sbAdvancedAlertBox: return 9
    case .sbPaste: return 10
    case .UNRECOGNIZED(let i): return i
    }
  }

  // The compiler won't synthesize support with the UNRECOGNIZED case.
  static let allCases: [IAnts_SBMessageType] = [
    .sbUnknown,
    .sbShowAlert,
    .sbShowToast,
    .sbTouchEvent,
    .sbNotification,
    .sbForegroundApp,
    .sbScreenshot,
    .sbLockScreen,
    .sbUnlockScreen,
    .sbAdvancedAlertBox,
    .sbPaste,
  ]

}

/// 弹窗，无回复
struct IAnts_SB_Alert: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var title: String = String()

  var content: String = String()

  var dismissTime: Int32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// Toast 无回复
struct IAnts_SB_Toast: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var content: String = String()

  var type: Int32 = 0

  var duration: Float = 0

  var position: Int32 = 0

  var fontSize: Int32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 触摸事件，无回复
struct IAnts_SB_TouchEvent: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var x: Int32 = 0

  var y: Int32 = 0

  var action: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 通知，无回复
struct IAnts_SB_Notification: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var title: String = String()

  var body: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 前台应用，有回复
struct IAnts_SB_ForegroundApp: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 请求ID（请求和响应都需要）
  var requestID: String = String()

  /// 响应时填充：应用Bundle ID
  var bundleID: String = String()

  /// 响应时填充：应用名称（可选）
  var appName: String = String()

  /// 响应时填充：是否成功获取
  var success: Bool = false

  /// 响应时填充：错误信息（可选）
  var errorMessage: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 截图，有回复 
/// 两种情况：1. 请求截图只保存到本地  2. 请求截图（isBase64==True）并返回图片base64
struct IAnts_SB_Screenshot: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 请求ID（请求和响应都需要）
  var requestID: String = String()

  /// 响应时填充：保存路径
  var path: String = String()

  /// 请求时填充：是否返回base64数据
  var isBase64: Bool = false

  /// 请求时填充：是否开启压缩
  var isCompress: Bool = false

  /// 响应时填充：base64图片数据（如果请求了base64）
  var data: String = String()

  /// 响应时填充：是否成功
  var success: Bool = false

  /// 响应时填充：错误信息（可选）
  var errorMessage: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 锁屏动作，无回复
struct IAnts_SB_LockScreen: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 解锁动作，无回复
struct IAnts_SB_UnlockScreen: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 高级弹窗，无回复
struct IAnts_SB_AdvancedAlert: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var title: String = String()

  var content: String = String()

  var defaultButtonTitle: String = String()

  var alternateButtonTitle: String = String()

  var otherButtonTitle: String = String()

  var checkBoxTitles: [String] = []

  var textFieldTitles: [String] = []

  var textFieldValues: [String] = []

  var popUpTitles: [String] = []

  var popUpSelection: Int32 = 0

  var progressValue: Float = 0

  var iconURL: String = String()

  var dismissTime: Int32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 发送粘贴内容，无回复
struct IAnts_SB_Paste: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var content: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// SpringBoard 完整消息
struct IAnts_SBMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var messageType: IAnts_SBMessage.OneOf_MessageType? = nil

  var commonType: IAnts_CommonMessageType {
    get {
      if case .commonType(let v)? = messageType {return v}
      return .heartbeat
    }
    set {messageType = .commonType(newValue)}
  }

  var sbType: IAnts_SBMessageType {
    get {
      if case .sbType(let v)? = messageType {return v}
      return .sbUnknown
    }
    set {messageType = .sbType(newValue)}
  }

  var body: IAnts_SBMessage.OneOf_Body? = nil

  /// 公共消息
  var heartbeat: IAnts_Heartbeat {
    get {
      if case .heartbeat(let v)? = body {return v}
      return IAnts_Heartbeat()
    }
    set {body = .heartbeat(newValue)}
  }

  var error: IAnts_ErrorMessage {
    get {
      if case .error(let v)? = body {return v}
      return IAnts_ErrorMessage()
    }
    set {body = .error(newValue)}
  }

  /// SB 专用消息
  var sbAlert: IAnts_SB_Alert {
    get {
      if case .sbAlert(let v)? = body {return v}
      return IAnts_SB_Alert()
    }
    set {body = .sbAlert(newValue)}
  }

  var sbToast: IAnts_SB_Toast {
    get {
      if case .sbToast(let v)? = body {return v}
      return IAnts_SB_Toast()
    }
    set {body = .sbToast(newValue)}
  }

  var sbTouchEvent: IAnts_SB_TouchEvent {
    get {
      if case .sbTouchEvent(let v)? = body {return v}
      return IAnts_SB_TouchEvent()
    }
    set {body = .sbTouchEvent(newValue)}
  }

  var sbForegroundApp: IAnts_SB_ForegroundApp {
    get {
      if case .sbForegroundApp(let v)? = body {return v}
      return IAnts_SB_ForegroundApp()
    }
    set {body = .sbForegroundApp(newValue)}
  }

  var sbNotification: IAnts_SB_Notification {
    get {
      if case .sbNotification(let v)? = body {return v}
      return IAnts_SB_Notification()
    }
    set {body = .sbNotification(newValue)}
  }

  var sbScreenshot: IAnts_SB_Screenshot {
    get {
      if case .sbScreenshot(let v)? = body {return v}
      return IAnts_SB_Screenshot()
    }
    set {body = .sbScreenshot(newValue)}
  }

  var sbLockScreen: IAnts_SB_LockScreen {
    get {
      if case .sbLockScreen(let v)? = body {return v}
      return IAnts_SB_LockScreen()
    }
    set {body = .sbLockScreen(newValue)}
  }

  var sbUnlockScreen: IAnts_SB_UnlockScreen {
    get {
      if case .sbUnlockScreen(let v)? = body {return v}
      return IAnts_SB_UnlockScreen()
    }
    set {body = .sbUnlockScreen(newValue)}
  }

  var sbAdvancedAlert: IAnts_SB_AdvancedAlert {
    get {
      if case .sbAdvancedAlert(let v)? = body {return v}
      return IAnts_SB_AdvancedAlert()
    }
    set {body = .sbAdvancedAlert(newValue)}
  }

  var sbPaste: IAnts_SB_Paste {
    get {
      if case .sbPaste(let v)? = body {return v}
      return IAnts_SB_Paste()
    }
    set {body = .sbPaste(newValue)}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  enum OneOf_MessageType: Equatable, Sendable {
    case commonType(IAnts_CommonMessageType)
    case sbType(IAnts_SBMessageType)

  }

  enum OneOf_Body: Equatable, Sendable {
    /// 公共消息
    case heartbeat(IAnts_Heartbeat)
    case error(IAnts_ErrorMessage)
    /// SB 专用消息
    case sbAlert(IAnts_SB_Alert)
    case sbToast(IAnts_SB_Toast)
    case sbTouchEvent(IAnts_SB_TouchEvent)
    case sbForegroundApp(IAnts_SB_ForegroundApp)
    case sbNotification(IAnts_SB_Notification)
    case sbScreenshot(IAnts_SB_Screenshot)
    case sbLockScreen(IAnts_SB_LockScreen)
    case sbUnlockScreen(IAnts_SB_UnlockScreen)
    case sbAdvancedAlert(IAnts_SB_AdvancedAlert)
    case sbPaste(IAnts_SB_Paste)

  }

  init() {}
}

// MARK: - Code below here is support for the SwiftProtobuf runtime.

fileprivate let _protobuf_package = "iAnts"

extension IAnts_SBMessageType: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "SB_UNKNOWN"),
    1: .same(proto: "SB_SHOW_ALERT"),
    2: .same(proto: "SB_SHOW_TOAST"),
    3: .same(proto: "SB_TOUCH_EVENT"),
    4: .same(proto: "SB_NOTIFICATION"),
    5: .same(proto: "SB_FOREGROUND_APP"),
    6: .same(proto: "SB_SCREENSHOT"),
    7: .same(proto: "SB_LOCK_SCREEN"),
    8: .same(proto: "SB_UNLOCK_SCREEN"),
    9: .same(proto: "SB_ADVANCED_ALERT_BOX"),
    10: .same(proto: "SB_PASTE"),
  ]
}

extension IAnts_SB_Alert: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_Alert"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "title"),
    2: .same(proto: "content"),
    3: .same(proto: "dismissTime"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.title) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.content) }()
      case 3: try { try decoder.decodeSingularInt32Field(value: &self.dismissTime) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.title.isEmpty {
      try visitor.visitSingularStringField(value: self.title, fieldNumber: 1)
    }
    if !self.content.isEmpty {
      try visitor.visitSingularStringField(value: self.content, fieldNumber: 2)
    }
    if self.dismissTime != 0 {
      try visitor.visitSingularInt32Field(value: self.dismissTime, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_Alert, rhs: IAnts_SB_Alert) -> Bool {
    if lhs.title != rhs.title {return false}
    if lhs.content != rhs.content {return false}
    if lhs.dismissTime != rhs.dismissTime {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_Toast: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_Toast"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "content"),
    2: .same(proto: "type"),
    3: .same(proto: "duration"),
    4: .same(proto: "position"),
    5: .same(proto: "fontSize"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.content) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self.type) }()
      case 3: try { try decoder.decodeSingularFloatField(value: &self.duration) }()
      case 4: try { try decoder.decodeSingularInt32Field(value: &self.position) }()
      case 5: try { try decoder.decodeSingularInt32Field(value: &self.fontSize) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.content.isEmpty {
      try visitor.visitSingularStringField(value: self.content, fieldNumber: 1)
    }
    if self.type != 0 {
      try visitor.visitSingularInt32Field(value: self.type, fieldNumber: 2)
    }
    if self.duration.bitPattern != 0 {
      try visitor.visitSingularFloatField(value: self.duration, fieldNumber: 3)
    }
    if self.position != 0 {
      try visitor.visitSingularInt32Field(value: self.position, fieldNumber: 4)
    }
    if self.fontSize != 0 {
      try visitor.visitSingularInt32Field(value: self.fontSize, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_Toast, rhs: IAnts_SB_Toast) -> Bool {
    if lhs.content != rhs.content {return false}
    if lhs.type != rhs.type {return false}
    if lhs.duration != rhs.duration {return false}
    if lhs.position != rhs.position {return false}
    if lhs.fontSize != rhs.fontSize {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_TouchEvent: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_TouchEvent"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "x"),
    2: .same(proto: "y"),
    3: .same(proto: "action"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularInt32Field(value: &self.x) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self.y) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.action) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.x != 0 {
      try visitor.visitSingularInt32Field(value: self.x, fieldNumber: 1)
    }
    if self.y != 0 {
      try visitor.visitSingularInt32Field(value: self.y, fieldNumber: 2)
    }
    if !self.action.isEmpty {
      try visitor.visitSingularStringField(value: self.action, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_TouchEvent, rhs: IAnts_SB_TouchEvent) -> Bool {
    if lhs.x != rhs.x {return false}
    if lhs.y != rhs.y {return false}
    if lhs.action != rhs.action {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_Notification: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_Notification"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "title"),
    2: .same(proto: "body"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.title) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.body) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.title.isEmpty {
      try visitor.visitSingularStringField(value: self.title, fieldNumber: 1)
    }
    if !self.body.isEmpty {
      try visitor.visitSingularStringField(value: self.body, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_Notification, rhs: IAnts_SB_Notification) -> Bool {
    if lhs.title != rhs.title {return false}
    if lhs.body != rhs.body {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_ForegroundApp: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_ForegroundApp"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "request_id"),
    2: .standard(proto: "bundle_id"),
    3: .standard(proto: "app_name"),
    4: .same(proto: "success"),
    5: .standard(proto: "error_message"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.requestID) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.bundleID) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.appName) }()
      case 4: try { try decoder.decodeSingularBoolField(value: &self.success) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.errorMessage) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.requestID.isEmpty {
      try visitor.visitSingularStringField(value: self.requestID, fieldNumber: 1)
    }
    if !self.bundleID.isEmpty {
      try visitor.visitSingularStringField(value: self.bundleID, fieldNumber: 2)
    }
    if !self.appName.isEmpty {
      try visitor.visitSingularStringField(value: self.appName, fieldNumber: 3)
    }
    if self.success != false {
      try visitor.visitSingularBoolField(value: self.success, fieldNumber: 4)
    }
    if !self.errorMessage.isEmpty {
      try visitor.visitSingularStringField(value: self.errorMessage, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_ForegroundApp, rhs: IAnts_SB_ForegroundApp) -> Bool {
    if lhs.requestID != rhs.requestID {return false}
    if lhs.bundleID != rhs.bundleID {return false}
    if lhs.appName != rhs.appName {return false}
    if lhs.success != rhs.success {return false}
    if lhs.errorMessage != rhs.errorMessage {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_Screenshot: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_Screenshot"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "request_id"),
    2: .same(proto: "path"),
    3: .same(proto: "isBase64"),
    4: .same(proto: "isCompress"),
    5: .same(proto: "data"),
    6: .same(proto: "success"),
    7: .standard(proto: "error_message"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.requestID) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.path) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self.isBase64) }()
      case 4: try { try decoder.decodeSingularBoolField(value: &self.isCompress) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.data) }()
      case 6: try { try decoder.decodeSingularBoolField(value: &self.success) }()
      case 7: try { try decoder.decodeSingularStringField(value: &self.errorMessage) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.requestID.isEmpty {
      try visitor.visitSingularStringField(value: self.requestID, fieldNumber: 1)
    }
    if !self.path.isEmpty {
      try visitor.visitSingularStringField(value: self.path, fieldNumber: 2)
    }
    if self.isBase64 != false {
      try visitor.visitSingularBoolField(value: self.isBase64, fieldNumber: 3)
    }
    if self.isCompress != false {
      try visitor.visitSingularBoolField(value: self.isCompress, fieldNumber: 4)
    }
    if !self.data.isEmpty {
      try visitor.visitSingularStringField(value: self.data, fieldNumber: 5)
    }
    if self.success != false {
      try visitor.visitSingularBoolField(value: self.success, fieldNumber: 6)
    }
    if !self.errorMessage.isEmpty {
      try visitor.visitSingularStringField(value: self.errorMessage, fieldNumber: 7)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_Screenshot, rhs: IAnts_SB_Screenshot) -> Bool {
    if lhs.requestID != rhs.requestID {return false}
    if lhs.path != rhs.path {return false}
    if lhs.isBase64 != rhs.isBase64 {return false}
    if lhs.isCompress != rhs.isCompress {return false}
    if lhs.data != rhs.data {return false}
    if lhs.success != rhs.success {return false}
    if lhs.errorMessage != rhs.errorMessage {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_LockScreen: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_LockScreen"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_LockScreen, rhs: IAnts_SB_LockScreen) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_UnlockScreen: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_UnlockScreen"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_UnlockScreen, rhs: IAnts_SB_UnlockScreen) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_AdvancedAlert: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_AdvancedAlert"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "title"),
    2: .same(proto: "content"),
    3: .same(proto: "defaultButtonTitle"),
    4: .same(proto: "alternateButtonTitle"),
    5: .same(proto: "otherButtonTitle"),
    6: .same(proto: "checkBoxTitles"),
    7: .same(proto: "textFieldTitles"),
    8: .same(proto: "textFieldValues"),
    9: .same(proto: "popUpTitles"),
    10: .same(proto: "popUpSelection"),
    11: .same(proto: "progressValue"),
    12: .same(proto: "iconURL"),
    13: .same(proto: "dismissTime"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.title) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.content) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.defaultButtonTitle) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.alternateButtonTitle) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.otherButtonTitle) }()
      case 6: try { try decoder.decodeRepeatedStringField(value: &self.checkBoxTitles) }()
      case 7: try { try decoder.decodeRepeatedStringField(value: &self.textFieldTitles) }()
      case 8: try { try decoder.decodeRepeatedStringField(value: &self.textFieldValues) }()
      case 9: try { try decoder.decodeRepeatedStringField(value: &self.popUpTitles) }()
      case 10: try { try decoder.decodeSingularInt32Field(value: &self.popUpSelection) }()
      case 11: try { try decoder.decodeSingularFloatField(value: &self.progressValue) }()
      case 12: try { try decoder.decodeSingularStringField(value: &self.iconURL) }()
      case 13: try { try decoder.decodeSingularInt32Field(value: &self.dismissTime) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.title.isEmpty {
      try visitor.visitSingularStringField(value: self.title, fieldNumber: 1)
    }
    if !self.content.isEmpty {
      try visitor.visitSingularStringField(value: self.content, fieldNumber: 2)
    }
    if !self.defaultButtonTitle.isEmpty {
      try visitor.visitSingularStringField(value: self.defaultButtonTitle, fieldNumber: 3)
    }
    if !self.alternateButtonTitle.isEmpty {
      try visitor.visitSingularStringField(value: self.alternateButtonTitle, fieldNumber: 4)
    }
    if !self.otherButtonTitle.isEmpty {
      try visitor.visitSingularStringField(value: self.otherButtonTitle, fieldNumber: 5)
    }
    if !self.checkBoxTitles.isEmpty {
      try visitor.visitRepeatedStringField(value: self.checkBoxTitles, fieldNumber: 6)
    }
    if !self.textFieldTitles.isEmpty {
      try visitor.visitRepeatedStringField(value: self.textFieldTitles, fieldNumber: 7)
    }
    if !self.textFieldValues.isEmpty {
      try visitor.visitRepeatedStringField(value: self.textFieldValues, fieldNumber: 8)
    }
    if !self.popUpTitles.isEmpty {
      try visitor.visitRepeatedStringField(value: self.popUpTitles, fieldNumber: 9)
    }
    if self.popUpSelection != 0 {
      try visitor.visitSingularInt32Field(value: self.popUpSelection, fieldNumber: 10)
    }
    if self.progressValue.bitPattern != 0 {
      try visitor.visitSingularFloatField(value: self.progressValue, fieldNumber: 11)
    }
    if !self.iconURL.isEmpty {
      try visitor.visitSingularStringField(value: self.iconURL, fieldNumber: 12)
    }
    if self.dismissTime != 0 {
      try visitor.visitSingularInt32Field(value: self.dismissTime, fieldNumber: 13)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_AdvancedAlert, rhs: IAnts_SB_AdvancedAlert) -> Bool {
    if lhs.title != rhs.title {return false}
    if lhs.content != rhs.content {return false}
    if lhs.defaultButtonTitle != rhs.defaultButtonTitle {return false}
    if lhs.alternateButtonTitle != rhs.alternateButtonTitle {return false}
    if lhs.otherButtonTitle != rhs.otherButtonTitle {return false}
    if lhs.checkBoxTitles != rhs.checkBoxTitles {return false}
    if lhs.textFieldTitles != rhs.textFieldTitles {return false}
    if lhs.textFieldValues != rhs.textFieldValues {return false}
    if lhs.popUpTitles != rhs.popUpTitles {return false}
    if lhs.popUpSelection != rhs.popUpSelection {return false}
    if lhs.progressValue != rhs.progressValue {return false}
    if lhs.iconURL != rhs.iconURL {return false}
    if lhs.dismissTime != rhs.dismissTime {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_Paste: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_Paste"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "content"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.content) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.content.isEmpty {
      try visitor.visitSingularStringField(value: self.content, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_Paste, rhs: IAnts_SB_Paste) -> Bool {
    if lhs.content != rhs.content {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SBMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SBMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "common_type"),
    2: .standard(proto: "sb_type"),
    10: .same(proto: "heartbeat"),
    11: .same(proto: "error"),
    20: .standard(proto: "sb_alert"),
    21: .standard(proto: "sb_toast"),
    22: .standard(proto: "sb_touch_event"),
    23: .standard(proto: "sb_foreground_app"),
    24: .standard(proto: "sb_notification"),
    25: .standard(proto: "sb_screenshot"),
    26: .standard(proto: "sb_lock_screen"),
    27: .standard(proto: "sb_unlock_screen"),
    28: .standard(proto: "sb_advanced_alert"),
    29: .standard(proto: "sb_paste"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try {
        var v: IAnts_CommonMessageType?
        try decoder.decodeSingularEnumField(value: &v)
        if let v = v {
          if self.messageType != nil {try decoder.handleConflictingOneOf()}
          self.messageType = .commonType(v)
        }
      }()
      case 2: try {
        var v: IAnts_SBMessageType?
        try decoder.decodeSingularEnumField(value: &v)
        if let v = v {
          if self.messageType != nil {try decoder.handleConflictingOneOf()}
          self.messageType = .sbType(v)
        }
      }()
      case 10: try {
        var v: IAnts_Heartbeat?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .heartbeat(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .heartbeat(v)
        }
      }()
      case 11: try {
        var v: IAnts_ErrorMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .error(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .error(v)
        }
      }()
      case 20: try {
        var v: IAnts_SB_Alert?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .sbAlert(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .sbAlert(v)
        }
      }()
      case 21: try {
        var v: IAnts_SB_Toast?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .sbToast(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .sbToast(v)
        }
      }()
      case 22: try {
        var v: IAnts_SB_TouchEvent?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .sbTouchEvent(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .sbTouchEvent(v)
        }
      }()
      case 23: try {
        var v: IAnts_SB_ForegroundApp?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .sbForegroundApp(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .sbForegroundApp(v)
        }
      }()
      case 24: try {
        var v: IAnts_SB_Notification?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .sbNotification(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .sbNotification(v)
        }
      }()
      case 25: try {
        var v: IAnts_SB_Screenshot?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .sbScreenshot(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .sbScreenshot(v)
        }
      }()
      case 26: try {
        var v: IAnts_SB_LockScreen?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .sbLockScreen(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .sbLockScreen(v)
        }
      }()
      case 27: try {
        var v: IAnts_SB_UnlockScreen?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .sbUnlockScreen(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .sbUnlockScreen(v)
        }
      }()
      case 28: try {
        var v: IAnts_SB_AdvancedAlert?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .sbAdvancedAlert(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .sbAdvancedAlert(v)
        }
      }()
      case 29: try {
        var v: IAnts_SB_Paste?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .sbPaste(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .sbPaste(v)
        }
      }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    switch self.messageType {
    case .commonType?: try {
      guard case .commonType(let v)? = self.messageType else { preconditionFailure() }
      try visitor.visitSingularEnumField(value: v, fieldNumber: 1)
    }()
    case .sbType?: try {
      guard case .sbType(let v)? = self.messageType else { preconditionFailure() }
      try visitor.visitSingularEnumField(value: v, fieldNumber: 2)
    }()
    case nil: break
    }
    switch self.body {
    case .heartbeat?: try {
      guard case .heartbeat(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 10)
    }()
    case .error?: try {
      guard case .error(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 11)
    }()
    case .sbAlert?: try {
      guard case .sbAlert(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 20)
    }()
    case .sbToast?: try {
      guard case .sbToast(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 21)
    }()
    case .sbTouchEvent?: try {
      guard case .sbTouchEvent(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 22)
    }()
    case .sbForegroundApp?: try {
      guard case .sbForegroundApp(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 23)
    }()
    case .sbNotification?: try {
      guard case .sbNotification(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 24)
    }()
    case .sbScreenshot?: try {
      guard case .sbScreenshot(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 25)
    }()
    case .sbLockScreen?: try {
      guard case .sbLockScreen(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 26)
    }()
    case .sbUnlockScreen?: try {
      guard case .sbUnlockScreen(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 27)
    }()
    case .sbAdvancedAlert?: try {
      guard case .sbAdvancedAlert(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 28)
    }()
    case .sbPaste?: try {
      guard case .sbPaste(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 29)
    }()
    case nil: break
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SBMessage, rhs: IAnts_SBMessage) -> Bool {
    if lhs.messageType != rhs.messageType {return false}
    if lhs.body != rhs.body {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}
