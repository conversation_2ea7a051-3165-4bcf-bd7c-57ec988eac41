//
//  iAntsSocketPB.m
//  iAnts Socket Protobuf Bridge
//
//  Created by iAnts on 2024/01/01.
//  Copyright © 2024 iAnts. All rights reserved.
//

#import "iAntsSocketPB.h"
#import "libiAntsSocketPB-Swift.h"

#pragma mark - IAntsMessage Implementation

@implementation IAntsMessage

- (instancetype)initWithType:(IAntsMessageType)type content:(NSDictionary *)content {
    self = [super init];
    if (self) {
        _type = type;
        _content = content ?: @{};
    }
    return self;
}

+ (nullable instancetype)messageFromData:(NSData *)data error:(NSError **)error {
    if (!data || data.length == 0) {
        if (error) {
            *error = [NSError errorWithDomain:@"IAntsProtobuf" 
                                        code:-1001 
                                    userInfo:@{NSLocalizedDescriptionKey: @"数据为空"}];
        }
        return nil;
    }
    
    // 使用通用反序列化接口
    NSDictionary *result = [[IAntsProtobuf shared] deserializeData:data error:error];
    if (!result) {
        return nil;
    }
    
    IAntsMessageType type = (IAntsMessageType)[result[@"type"] integerValue];
    NSDictionary *content = result[@"content"];
    
    return [[IAntsMessage alloc] initWithType:type content:content];
}

- (nullable NSData *)serializeToData:(NSError **)error {
    // 使用通用序列化接口
    return [[IAntsProtobuf shared] serializeMessageType:self.type content:self.content error:error];
}

- (NSString *)description {
    return [NSString stringWithFormat:@"<IAntsMessage: type=%ld, content=%@>", 
            (long)self.type, self.content];
}

@end

#pragma mark - IAntsProtobuf Implementation

@implementation IAntsProtobuf

+ (instancetype)shared {
    static IAntsProtobuf *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (nullable NSData *)serializeMessageType:(IAntsMessageType)messageType 
                                  content:(NSDictionary *)content 
                                    error:(NSError **)error {
    if (!content) {
        content = @{};
    }
    
    // 调用Swift桥接类进行序列化
    NSData *data = [[IAntsProtobufSwiftBridge shared] serializeMessageWithType:(int)messageType 
                                                                       content:content];
    if (!data && error) {
        *error = [NSError errorWithDomain:@"IAntsProtobuf" 
                                    code:-1003 
                                userInfo:@{NSLocalizedDescriptionKey: @"protobuf序列化失败"}];
    }
    
    return data;
}

- (nullable NSDictionary *)deserializeData:(NSData *)data error:(NSError **)error {
    if (!data || data.length == 0) {
        if (error) {
            *error = [NSError errorWithDomain:@"IAntsProtobuf" 
                                        code:-1001 
                                    userInfo:@{NSLocalizedDescriptionKey: @"数据为空"}];
        }
        return nil;
    }
    
    // 调用Swift桥接类进行反序列化
    NSDictionary *result = [[IAntsProtobufSwiftBridge shared] deserializeData:data];
    if (!result && error) {
        *error = [NSError errorWithDomain:@"IAntsProtobuf" 
                                    code:-1002 
                                userInfo:@{NSLocalizedDescriptionKey: @"protobuf反序列化失败"}];
    }
    
    return result;
}

- (BOOL)isValidMessageType:(IAntsMessageType)messageType {
    // 调用Swift桥接类的方法
    return [[IAntsProtobufSwiftBridge shared] isValidMessageType:(int)messageType];
}

@end

#pragma mark - IAntsMessageHelper Implementation

@implementation IAntsMessageHelper

+ (IAntsMessage *)heartbeatWithVersion:(NSString *)version {
    NSDictionary *content = @{
        @"version": version ?: @""
    };
    return [[IAntsMessage alloc] initWithType:IAntsMessageTypeHeartbeat content:content];
}

+ (IAntsMessage *)alertWithTitle:(NSString *)title 
                         content:(NSString *)content 
                     dismissTime:(int32_t)dismissTime {
    NSDictionary *messageContent = @{
        @"title": title ?: @"",
        @"content": content ?: @"",
        @"dismissTime": @(dismissTime)
    };
    return [[IAntsMessage alloc] initWithType:IAntsMessageTypeSBShowAlert content:messageContent];
}

+ (IAntsMessage *)pasteWithContent:(NSString *)content {
    NSDictionary *messageContent = @{
        @"content": content ?: @""
    };
    return [[IAntsMessage alloc] initWithType:IAntsMessageTypeSBShowPaste content:messageContent];
}

+ (IAntsMessage *)touchEventWithX:(int32_t)x y:(int32_t)y action:(NSString *)action {
    NSDictionary *content = @{
        @"x": @(x),
        @"y": @(y),
        @"action": action ?: @""
    };
    return [[IAntsMessage alloc] initWithType:IAntsMessageTypeSBTouchEvent content:content];
}

+ (IAntsMessage *)messageWithType:(IAntsMessageType)type content:(NSDictionary *)content {
    return [[IAntsMessage alloc] initWithType:type content:content ?: @{}];
}

@end