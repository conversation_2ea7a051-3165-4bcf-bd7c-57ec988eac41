//
//  iAntsSocketPB.swift
//  iAnts Socket Protobuf Bridge
//
//  Created by iAnts on 2024/01/01.
//  Copyright © 2024 iAnts. All rights reserved.
//

import Foundation
import SwiftProtobuf

/// Swift实现的protobuf桥接类，供OC调用
@objc public class IAntsProtobufSwiftBridge: NSObject {
    
    /// 单例对象
    @objc public static let shared = IAntsProtobufSwiftBridge()
    
    private override init() {
        super.init()
    }
    
    /// 将OC消息类型转换为protobuf消息类型和消息体
    private func createSBMessage(type: Int, content: [String: Any]) -> IAnts_SBMessage? {
        var message = IAnts_SBMessage()
        
        switch type {
        case 0: // IAntsMessageTypeHeartbeat
            message.messageType = .commonType(.heartbeat)
            var heartbeat = IAnts_Heartbeat()
            if let version = content["version"] as? String {
                heartbeat.version = version
            }
            message.body = .heartbeat(heartbeat)
            
        case 999: // IAntsMessageTypeError
            message.messageType = .commonType(.error)
            var error = IAnts_ErrorMessage()
            if let text = content["text"] as? String {
                error.text = text
            }
            message.body = .error(error)
            
        case 100: // IAntsMessageTypeSBShowAlert
            message.messageType = .sbType(.sbShowAlert)
            var alert = IAnts_SB_Alert()
            if let title = content["title"] as? String { alert.title = title }
            if let content = content["content"] as? String { alert.content = content }
            if let dismissTime = content["dismissTime"] as? Int32 { alert.dismissTime = dismissTime }
            else if let dismissTime = content["dismissTime"] as? Int { alert.dismissTime = Int32(dismissTime) }
            message.body = .sbAlert(alert)
            
        case 101: // IAntsMessageTypeSBShowToast
            message.messageType = .sbType(.sbShowToast)
            var toast = IAnts_SB_Toast()
            if let content = content["content"] as? String { toast.content = content }
            if let type = content["type"] as? Int32 { toast.type = type }
            else if let type = content["type"] as? Int { toast.type = Int32(type) }
            if let duration = content["duration"] as? Float { toast.duration = duration }
            else if let duration = content["duration"] as? Double { toast.duration = Float(duration) }
            if let position = content["position"] as? Int32 { toast.position = position }
            else if let position = content["position"] as? Int { toast.position = Int32(position) }
            if let fontSize = content["fontSize"] as? Int32 { toast.fontSize = fontSize }
            else if let fontSize = content["fontSize"] as? Int { toast.fontSize = Int32(fontSize) }
            message.body = .sbToast(toast)
            
        case 102: // IAntsMessageTypeSBTouchEvent
            message.messageType = .sbType(.sbTouchEvent)
            var touchEvent = IAnts_SB_TouchEvent()
            if let x = content["x"] as? Int32 { touchEvent.x = x }
            else if let x = content["x"] as? Int { touchEvent.x = Int32(x) }
            if let y = content["y"] as? Int32 { touchEvent.y = y }
            else if let y = content["y"] as? Int { touchEvent.y = Int32(y) }
            if let action = content["action"] as? String { touchEvent.action = action }
            message.body = .sbTouchEvent(touchEvent)
            
        case 104: // IAntsMessageTypeSBForegroundApp
            message.messageType = .sbType(.sbForegroundApp)
            var foregroundApp = IAnts_SB_ForegroundApp()
            if let requestID = content["request_id"] as? String { foregroundApp.requestID = requestID }
            if let bundleID = content["bundle_id"] as? String { foregroundApp.bundleID = bundleID }
            if let appName = content["app_name"] as? String { foregroundApp.appName = appName }
            if let success = content["success"] as? Bool { foregroundApp.success = success }
            if let errorMessage = content["error_message"] as? String { foregroundApp.errorMessage = errorMessage }
            message.body = .sbForegroundApp(foregroundApp)
            
        case 105: // IAntsMessageTypeSBNotification
            message.messageType = .sbType(.sbNotification)
            var notification = IAnts_SB_Notification()
            if let title = content["title"] as? String { notification.title = title }
            if let body = content["body"] as? String { notification.body = body }
            message.body = .sbNotification(notification)
            
        case 106: // IAntsMessageTypeSBScreenshot
            message.messageType = .sbType(.sbScreenshot)
            var screenshot = IAnts_SB_Screenshot()
            if let requestID = content["request_id"] as? String { screenshot.requestID = requestID }
            if let path = content["path"] as? String { screenshot.path = path }
            if let isBase64 = content["isBase64"] as? Bool { screenshot.isBase64 = isBase64 }
            if let isCompress = content["isCompress"] as? Bool { screenshot.isCompress = isCompress }
            if let data = content["data"] as? String { screenshot.data = data }
            if let success = content["success"] as? Bool { screenshot.success = success }
            if let errorMessage = content["error_message"] as? String { screenshot.errorMessage = errorMessage }
            message.body = .sbScreenshot(screenshot)
            
        case 107: // IAntsMessageTypeSBLockScreen
            message.messageType = .sbType(.sbLockScreen)
            message.body = .sbLockScreen(IAnts_SB_LockScreen())
            
        case 108: // IAntsMessageTypeSBUnlockScreen
            message.messageType = .sbType(.sbUnlockScreen)
            message.body = .sbUnlockScreen(IAnts_SB_UnlockScreen())
            
        case 109: // IAntsMessageTypeSBAdvancedAlertBox
            message.messageType = .sbType(.sbAdvancedAlertBox)
            var advancedAlert = IAnts_SB_AdvancedAlert()
            if let title = content["title"] as? String { advancedAlert.title = title }
            if let content = content["content"] as? String { advancedAlert.content = content }
            if let defaultButtonTitle = content["defaultButtonTitle"] as? String { advancedAlert.defaultButtonTitle = defaultButtonTitle }
            if let alternateButtonTitle = content["alternateButtonTitle"] as? String { advancedAlert.alternateButtonTitle = alternateButtonTitle }
            if let otherButtonTitle = content["otherButtonTitle"] as? String { advancedAlert.otherButtonTitle = otherButtonTitle }
            if let checkBoxTitles = content["checkBoxTitles"] as? [String] { advancedAlert.checkBoxTitles = checkBoxTitles }
            if let textFieldTitles = content["textFieldTitles"] as? [String] { advancedAlert.textFieldTitles = textFieldTitles }
            if let textFieldValues = content["textFieldValues"] as? [String] { advancedAlert.textFieldValues = textFieldValues }
            if let popUpTitles = content["popUpTitles"] as? [String] { advancedAlert.popUpTitles = popUpTitles }
            if let popUpSelection = content["popUpSelection"] as? Int32 { advancedAlert.popUpSelection = popUpSelection }
            else if let popUpSelection = content["popUpSelection"] as? Int { advancedAlert.popUpSelection = Int32(popUpSelection) }
            if let progressValue = content["progressValue"] as? Float { advancedAlert.progressValue = progressValue }
            else if let progressValue = content["progressValue"] as? Double { advancedAlert.progressValue = Float(progressValue) }
            if let iconURL = content["iconURL"] as? String { advancedAlert.iconURL = iconURL }
            if let dismissTime = content["dismissTime"] as? Int32 { advancedAlert.dismissTime = dismissTime }
            else if let dismissTime = content["dismissTime"] as? Int { advancedAlert.dismissTime = Int32(dismissTime) }
            message.body = .sbAdvancedAlert(advancedAlert)
            
        case 110: // IAntsMessageTypeSBShowPaste
            message.messageType = .sbType(.sbPaste)
            var paste = IAnts_SB_Paste()
            if let content = content["content"] as? String { paste.content = content }
            message.body = .sbPaste(paste)
            
        default:
            return nil
        }
        
        return message
    }
    
    /// 从SBMessage提取内容和类型
    private func extractFromSBMessage(_ message: IAnts_SBMessage) -> (type: Int, content: [String: Any]) {
        var type: Int = 0
        var content: [String: Any] = [:]
        
        // 确定消息类型
        switch message.messageType {
        case .commonType(let commonType):
            switch commonType {
            case .heartbeat:
                type = 0
            case .error:
                type = 999
            case .UNRECOGNIZED(let value):
                type = value
            }
        case .sbType(let sbType):
            switch sbType {
            case .sbShowAlert:
                type = 100
            case .sbShowToast:
                type = 101
            case .sbTouchEvent:
                type = 102
            case .sbNotification:
                type = 105
            case .sbForegroundApp:
                type = 104
            case .sbScreenshot:
                type = 106
            case .sbLockScreen:
                type = 107
            case .sbUnlockScreen:
                type = 108
            case .sbAdvancedAlertBox:
                type = 109
            case .sbPaste:
                type = 110
            case .UNRECOGNIZED(let value):
                type = value
            default:
                type = 0
            }
        case .none:
            type = 0
        }
        
        // 提取消息内容
        switch message.body {
        case .heartbeat(let heartbeat):
            content = ["version": heartbeat.version]
        case .error(let error):
            content = ["text": error.text]
        case .sbAlert(let alert):
            content = [
                "title": alert.title,
                "content": alert.content,
                "dismissTime": alert.dismissTime
            ]
        case .sbToast(let toast):
            content = [
                "content": toast.content,
                "type": toast.type,
                "duration": toast.duration,
                "position": toast.position,
                "fontSize": toast.fontSize
            ]
        case .sbTouchEvent(let touchEvent):
            content = [
                "x": touchEvent.x,
                "y": touchEvent.y,
                "action": touchEvent.action
            ]
        case .sbForegroundApp(let foregroundApp):
            content = [
                "request_id": foregroundApp.requestID,
                "bundle_id": foregroundApp.bundleID,
                "app_name": foregroundApp.appName,
                "success": foregroundApp.success,
                "error_message": foregroundApp.errorMessage
            ]
        case .sbNotification(let notification):
            content = [
                "title": notification.title,
                "body": notification.body
            ]
        case .sbScreenshot(let screenshot):
            content = [
                "request_id": screenshot.requestID,
                "path": screenshot.path,
                "isBase64": screenshot.isBase64,
                "isCompress": screenshot.isCompress,
                "data": screenshot.data,
                "success": screenshot.success,
                "error_message": screenshot.errorMessage
            ]
        case .sbLockScreen(_):
            content = [:]
        case .sbUnlockScreen(_):
            content = [:]
        case .sbAdvancedAlert(let advancedAlert):
            var result: [String: Any] = [
                "title": advancedAlert.title,
                "content": advancedAlert.content,
                "defaultButtonTitle": advancedAlert.defaultButtonTitle,
                "alternateButtonTitle": advancedAlert.alternateButtonTitle,
                "otherButtonTitle": advancedAlert.otherButtonTitle,
                "dismissTime": advancedAlert.dismissTime,
                "popUpSelection": advancedAlert.popUpSelection,
                "progressValue": advancedAlert.progressValue,
                "iconURL": advancedAlert.iconURL
            ]
            if !advancedAlert.checkBoxTitles.isEmpty { result["checkBoxTitles"] = advancedAlert.checkBoxTitles }
            if !advancedAlert.textFieldTitles.isEmpty { result["textFieldTitles"] = advancedAlert.textFieldTitles }
            if !advancedAlert.textFieldValues.isEmpty { result["textFieldValues"] = advancedAlert.textFieldValues }
            if !advancedAlert.popUpTitles.isEmpty { result["popUpTitles"] = advancedAlert.popUpTitles }
            content = result
        case .sbPaste(let paste):
            content = ["content": paste.content]
        case .none:
            content = [:]
        }
        
        return (type: type, content: content)
    }
    
    /// 序列化消息为二进制数据
    @objc public func serializeMessage(type: Int, content: [String: Any]) -> Data? {
        guard let message = createSBMessage(type: type, content: content) else {
            print("无法创建消息类型: \(type)")
            return nil
        }
        
        do {
            return try message.serializedData()
        } catch {
            print("序列化失败: \(error)")
            return nil
        }
    }
    
    /// 从二进制数据反序列化消息
    @objc public func deserializeData(_ data: Data) -> [String: Any]? {
        do {
            let message = try IAnts_SBMessage(serializedBytes: data)
            let result = extractFromSBMessage(message)
            return [
                "type": result.type,
                "content": result.content
            ]
        } catch {
            print("反序列化失败: \(error)")
            return nil
        }
    }
    
    /// 验证消息类型是否有效
    @objc public func isValidMessageType(_ type: Int) -> Bool {
        switch type {
        case 0, 999: // 通用消息
            return true
        case 100...110: // SpringBoard消息
            return true
        default:
            return false
        }
    }
}
