# 静态库目标
LIBRARY_NAME = libiAntsSocketPB

# 支持的架构
ARCHS = arm64 arm64e

# 目标平台
TARGET = iphone:clang:latest:15.0

include $(THEOS)/makefiles/common.mk

# Swift源文件和Objective-C源文件
$(LIBRARY_NAME)_FILES = $(shell find Sources -name "*.swift") $(shell find Sources -name "*.m")
$(LIBRARY_NAME)_FILES += ../common/LogC.m ../common/Log.swift

# 编译标志
$(LIBRARY_NAME)_CFLAGS = -fobjc-arc
ifeq ($(BUILD_TYPE), debug)
    $(LIBRARY_NAME)_CFLAGS += -DDEBUG=1
endif

# Swift编译标志 - 添加SwiftProtobuf框架路径
$(LIBRARY_NAME)_SWIFTFLAGS = -F./frameworks -framework SwiftProtobuf -parse-as-library

# 链接标志
$(LIBRARY_NAME)_LDFLAGS = -lc++ -framework Foundation -framework UIKit
$(LIBRARY_NAME)_LDFLAGS += -F./frameworks -framework SwiftProtobuf

# 头文件搜索路径 - 指向include目录
$(LIBRARY_NAME)_CFLAGS += -I./include -I../common

# 设置为静态库
$(LIBRARY_NAME)_LINKAGE_TYPE = static

include $(THEOS_MAKE_PATH)/library.mk

before-all::
	@echo "==> 清理静态库"
	rm -rf ./lib

# 编译后处理
after-all::
	@echo "==> 复制静态库到 ./lib 目录..."
	@mkdir -p ./lib
	@cp $(THEOS_OBJ_DIR)/libiAntsSocketPB.a ./lib/ 2>/dev/null || echo "Warning: 未找到libiAntsSocketPB.a文件"
	@echo "iAntsSocketPB静态库编译完成"
	@echo "头文件位置: include/iAntsSocketPB.h"
	@echo "库文件位置: ./lib/libiAntsSocketPB.a"

# 清理目标
clean::
	@echo "清理iAntsSocketPB构建文件..."
	rm -rf $(THEOS_OBJ_DIR)
	rm -rf .theos

.PHONY: clean install-headers
