// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "iAntsSocketPB",
    platforms: [.iOS(.v13), .macOS(.v10_15)],
    products: [
        // Products define the executables and libraries a package produces, making them visible to other packages.
        .library(
            name: "iAntsSocketPB",
            targets: ["iAntsSocketPB"]),
    ],
    dependencies: [
        // Dependencies declare other packages that this package depends on.
        .package(url: "https://github.com/apple/swift-protobuf.git", from: "1.26.0"),
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        .target(
            name: "iAntsSocketPB",
            dependencies: [
                .product(name: "SwiftProtobuf", package: "swift-protobuf"),
            ],
            sources: [
                "Sources/"
            ],
            publicHeadersPath: "Sources/",
            cSettings: [
                .headerSearchPath("Sources/"),
            ]
        ),
        .testTarget(
            name: "iAntsSocketPBTests",
            dependencies: ["iAntsSocketPB"]),
    ]
) 