//
//  iAntsSocketPB.h
//  iAnts Socket Protobuf Bridge
//
//  Created by iAnts on 2024/01/01.
//  Copyright © 2024 iAnts. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 消息类型枚举 (与protobuf保持一致)
typedef NS_ENUM(NSInteger, IAntsMessageType) {
    // 0-9：通用消息
    IAntsMessageTypeHeartbeat = 0,

    // 10-99：iAntsCore 指令
    IAntsMessageTypeIAntsVersion = 10,
    IAntsMessageTypeIAntsHTTPStart = 11,
    IAntsMessageTypeIAntsHTTPShutdown = 12,
    IAntsMessageTypeIAntsHTTPRestart = 13,

    // 100-199：SpringBoard 指令
    IAntsMessageTypeSBShowAlert = 100,
    IAntsMessageTypeSBShowToast = 101,
    IAntsMessageTypeSBTouchEvent = 102,
    IAntsMessageTypeSBScreenInfo = 103,
    IAntsMessageTypeSBForegroundApp = 104,
    IAntsMessageTypeSBNotification = 105,
    IAntsMessageTypeSBScreenshot = 106,
    IAntsMessageTypeSBLockScreen = 107,
    IAntsMessageTypeSBUnlockScreen = 108,
    IAntsMessageTypeSBAdvancedAlertBox = 109,
    IAntsMessageTypeSBShowPaste = 110,

    // 200-299：iAntsRTC 指令
    IAntsMessageTypeRTCStatus = 200,

    // 错误消息
    IAntsMessageTypeError = 999
};

/// OC消息包装类 - 简化版本
@interface IAntsMessage : NSObject

/// 消息类型
@property(nonatomic, assign) IAntsMessageType type;

/// 消息内容 (通用字典格式)
@property(nonatomic, strong) NSDictionary *content;

/// 便利初始化方法
- (instancetype)initWithType:(IAntsMessageType)type content:(NSDictionary *)content;

/// 从二进制数据创建消息 (protobuf反序列化)
+ (nullable instancetype)messageFromData:(NSData *)data error:(NSError **)error;

/// 序列化为二进制数据 (protobuf序列化)
- (nullable NSData *)serializeToData:(NSError **)error;

@end

/// Protobuf 桥接管理类 - 通用接口
@interface IAntsProtobuf : NSObject

/// 单例对象
+ (instancetype)shared;

/// 通用序列化方法 - 根据消息类型和数据字典生成protobuf数据
/// @param messageType 消息类型枚举值
/// @param content 消息内容字典
/// @param error 错误信息输出
/// @return 序列化后的二进制数据
- (nullable NSData *)serializeMessageType:(IAntsMessageType)messageType 
                                  content:(NSDictionary *)content 
                                    error:(NSError **)error;

/// 通用反序列化方法 - 从protobuf数据解析出消息类型和内容
/// @param data protobuf二进制数据
/// @param error 错误信息输出
/// @return 包含type和content的字典，格式：@{@"type": @(messageType), @"content": contentDict}
- (nullable NSDictionary *)deserializeData:(NSData *)data error:(NSError **)error;

/// 验证消息类型是否有效
/// @param messageType 消息类型枚举值
/// @return 是否为有效的消息类型
- (BOOL)isValidMessageType:(IAntsMessageType)messageType;

@end

/// 便利工厂类 - 提供常用消息的快速创建方法（可选使用）
@interface IAntsMessageHelper : NSObject

/// 创建心跳消息
+ (IAntsMessage *)heartbeatWithVersion:(NSString *)version;

/// 创建弹窗消息
+ (IAntsMessage *)alertWithTitle:(NSString *)title 
                         content:(NSString *)content 
                     dismissTime:(int32_t)dismissTime;

/// 创建粘贴板消息
+ (IAntsMessage *)pasteWithContent:(NSString *)content;

/// 创建触摸事件消息
+ (IAntsMessage *)touchEventWithX:(int32_t)x y:(int32_t)y action:(NSString *)action;

/// 创建通用消息 - 最灵活的方法
+ (IAntsMessage *)messageWithType:(IAntsMessageType)type content:(NSDictionary *)content;

@end

NS_ASSUME_NONNULL_END